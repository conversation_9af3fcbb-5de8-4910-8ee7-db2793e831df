{"branches": ["main"], "tagFormat": "${version}", "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], ["@semantic-release/exec", {"prepareCmd": "sed -i \"s/runpod\\/worker-comfyui:[0-9][0-9]*\\.[0-9][0-9]*\\.[0-9][0-9]*/runpod\\/worker-comfyui:${nextRelease.version}/g\" README.md"}], ["@semantic-release/git", {"assets": ["README.md", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} \n\n${nextRelease.notes}"}], "@semantic-release/github"]}