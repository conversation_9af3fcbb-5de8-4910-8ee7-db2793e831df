# Acknowledgments

- Thanks to [<PERSON><PERSON><PERSON>](https://github.com/blib-la) for providing the original repo (previously under `blib-la/runpod-worker-comfy`) to RunPod, to continue the open-source development of this worker.
- Thanks to [all contributors](https://github.com/runpod-workers/worker-comfyui/graphs/contributors) for your awesome work.
- Thanks to [<PERSON>](https://github.com/justinmerrell) from RunPod for [worker-1111](https://github.com/runpod-workers/worker-a1111), which was used to get inspired on how to create this worker.
- Thanks to [<PERSON>](https://github.com/ashleykleynhans) for [runpod-worker-a1111](https://github.com/ashleykleynhans/runpod-worker-a1111), which was used to get inspired on how to create this worker.
- Thanks to [comfyanonymous](https://github.com/comfyanonymous) for creating [ComfyUI](https://github.com/comfyanonymous/ComfyUI), which provides such an awesome API to interact with Stable Diffusion and beyond.
