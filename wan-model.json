{"3": {"inputs": {"seed": 435568154381893, "steps": 20, "cfg": 6, "sampler_name": "uni_pc", "scheduler": "simple", "denoise": 1, "model": ["56", 0], "positive": ["50", 0], "negative": ["50", 1], "latent_image": ["50", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"text": "An adorable fox with soft, fluffy fur and bright, expressive eyes. The camera moves slowly from far to near. In the background, a high-tech cityscape rises with towering skyscrapers. The sky is a mix of deep blues and purples, dotted with futuristic flying vehicles and shimmering stars. The overall atmosphere should be a blend of warmth and wonder, with a touch of sci-fi magic.", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "Overexposure, static, blurred details, subtitles, paintings, pictures, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, mutilated, redundant fingers, poorly painted hands, poorly painted faces, deformed, disfigured, deformed limbs, fused fingers, cluttered background, three legs, a lot of people in the background, upside down", "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "38": {"inputs": {"clip_name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "wan", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "39": {"inputs": {"vae_name": "wan_2.1_vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "49": {"inputs": {"clip_name": "clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "50": {"inputs": {"width": 832, "height": 480, "length": 33, "batch_size": 1, "positive": ["6", 0], "negative": ["7", 0], "vae": ["39", 0], "clip_vision_output": ["51", 0], "start_image": ["52", 0]}, "class_type": "WanImageToVideo", "_meta": {"title": "WanImageToVideo"}}, "51": {"inputs": {"crop": "none", "clip_vision": ["49", 0], "image": ["52", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "52": {"inputs": {"image": "input_image.png"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "54": {"inputs": {"images": ["8", 0], "filename_prefix": "wan_video_output"}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "56": {"inputs": {"unet_name": "wan2.1-i2v-14b-480p-Q3_K_S.gguf"}, "class_type": "UnetLoaderGGUF", "_meta": {"title": "Unet Loader (GGUF)"}}}