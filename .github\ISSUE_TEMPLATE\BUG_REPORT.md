---
name: "Bug report"
about: "Create a report to help us improve"
title: "[BUG]: …"
labels: "bug"
assignees: ""
---

**Describe the bug**

<!-- A clear and concise description of what the bug is.-->

**Repro MVP (Minimal Viable Procedure)**

<!-- Provide a minimal reproduction procedure.
      Include a simple set of steps or a small script that can reproduce the issue.
      Attach or link any relevant configuration files or Dockerfiles.
-->

**Expected behavior**

<!-- A clear and concise description of what you expected to happen. -->

**Screenshots**

<!-- If applicable, add screenshots to help explain your problem. -->

**Versions (please complete the following information):**

- Docker version: [e.g. 20.10.7]
- ComfyUI version: [e.g. 1.2.0]
- Host OS: [e.g. Ubuntu 20.04]

**Additional context**

<!-- Add any other context about the problem here, such as specific Docker commands or settings used. -->
