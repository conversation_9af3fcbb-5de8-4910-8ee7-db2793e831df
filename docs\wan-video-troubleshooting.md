# Wan Video Workflow Troubleshooting Guide

This guide helps you diagnose and fix issues with Wan video generation workflows in worker-comfyui.

## Quick Diagnosis

### 1. Check Required <PERSON>des

Run the node checker script to verify all required custom nodes are installed:

```bash
python check-wan-nodes.py
```

**Expected output if working:**

```
✅ WanImageToVideo - Available
✅ UnetLoaderGGUF - Available
✅ CLIPVisionLoader - Available
✅ CLIPVisionEncode - Available
```

### 2. Common Error Messages

#### "class_type 'WanImageToVideo' not found"

- **Cause**: ComfyUI-WanStartEndFramesNative custom node not installed
- **Fix**: Install the custom node (see [Installation Steps](#installation-steps))

#### "class_type 'UnetLoaderGGUF' not found"

- **Cause**: ComfyUI-GGUF custom node not installed
- **Fix**: Install the custom node (see [Installation Steps](#installation-steps))

#### "Model file not found: wan2.1-i2v-14b-480p-Q3_K_S.gguf"

- **Cause**: UNet model not downloaded or in wrong location
- **Fix**: Check model download (see [Model Verification](#model-verification))

#### "ffmpeg not found" or video encoding errors

- **Cause**: Missing video processing dependencies in Docker image
- **Fix**: Ensure Dockerfile.wan includes ffmpeg and OpenCV libraries (fixed in latest version)

## Installation Steps

### For Docker Builds (Recommended)

1. **Use the provided Dockerfile.wan:**

   ```bash
   docker build -f Dockerfile.wan -t wan-video .
   ```

2. **Verify the build completed successfully:**
   ```bash
   docker run --rm wan-video python check-wan-nodes.py
   ```

### For Manual Installation

1. **Install ComfyUI-WanStartEndFramesNative:**

   ```bash
   cd /comfyui/custom_nodes
   git clone https://github.com/Flow-two/ComfyUI-WanStartEndFramesNative.git
   cd ComfyUI-WanStartEndFramesNative
   pip install -r requirements.txt  # if requirements.txt exists
   ```

2. **Install ComfyUI-GGUF:**

   ```bash
   cd /comfyui/custom_nodes
   git clone https://github.com/city96/ComfyUI-GGUF.git
   cd ComfyUI-GGUF
   pip install -r requirements.txt
   ```

3. **Restart ComfyUI** to load the new nodes.

## Model Verification

### Check Model Files

Verify all required models are in the correct locations:

```bash
# UNet model (7.93 GB)
ls -la /comfyui/models/unet/wan2.1-i2v-14b-480p-Q3_K_S.gguf

# VAE model (508 MB)
ls -la /comfyui/models/vae/wan_2.1_vae.safetensors

# CLIP Vision model (2.5 GB)
ls -la /comfyui/models/clip_vision/clip_vision_h.safetensors

# Text Encoder model (4.7 GB)
ls -la /comfyui/models/clip/umt5_xxl_fp8_e4m3fn_scaled.safetensors
```

### Download Missing Models

If any models are missing, download them manually:

```bash
# UNet model
wget -O /comfyui/models/unet/wan2.1-i2v-14b-480p-Q3_K_S.gguf \
  https://huggingface.co/city96/Wan2.1-I2V-14B-480P-gguf/resolve/main/wan2.1-i2v-14b-480p-Q3_K_S.gguf

# VAE model
wget -O /comfyui/models/vae/wan_2.1_vae.safetensors \
  https://huggingface.co/motion-muse/Wan2.1/resolve/main/Wan2.1_VAE.safetensors

# CLIP Vision model
wget -O /comfyui/models/clip_vision/clip_vision_h.safetensors \
  https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors

# Text Encoder model
wget -O /comfyui/models/clip/umt5_xxl_fp8_e4m3fn_scaled.safetensors \
  https://huggingface.co/Evados/DiffSynth-Studio-Lora-Wan2.1-ComfyUI/resolve/main/umt5_xxl_fp8_e4m3fn_scaled.safetensors
```

## Workflow Validation

### Test with Simple Workflow

Use the provided test workflow to verify everything works:

```bash
# Test the workflow
curl -X POST http://localhost:8000/runsync \
  -H "Content-Type: application/json" \
  -d @wan-test-input.json
```

### Common Workflow Issues

1. **Missing class_type in SaveImage node:**

   - Check that node "54" in your workflow has `"class_type": "SaveImage"`

2. **Incorrect image reference:**
   - Ensure LoadImage node uses `"image": "input_image.png"` not `"pasted/image (1).png"`

## Advanced Debugging

### Enable Verbose Logging

Set environment variables for detailed logging:

```bash
export COMFY_LOG_LEVEL=DEBUG
export WEBSOCKET_TRACE=true
```

### Check ComfyUI Logs

Monitor ComfyUI startup logs for custom node loading:

```bash
# Look for these messages in ComfyUI logs:
# "Loading: /comfyui/custom_nodes/ComfyUI-WanStartEndFramesNative"
# "Loading: /comfyui/custom_nodes/ComfyUI-GGUF"
```

### Verify Python Dependencies

Check if required Python packages are installed:

```bash
python -c "import gguf; print('✅ gguf package available')"
```

### Verify System Dependencies

Check if video processing dependencies are available:

```bash
# Check ffmpeg installation
ffmpeg -version

# Check OpenCV libraries
ldconfig -p | grep libgl
ldconfig -p | grep libglib
```

## Version Compatibility

- **ComfyUI**: 0.3.30+ (included in base image)
- **Python**: 3.12+ (included in base image)
- **CUDA**: 12.6+ (for GPU acceleration)

## Getting Help

If you're still experiencing issues:

1. **Run the diagnostic script:** `python check-wan-nodes.py`
2. **Check the logs** for specific error messages
3. **Verify model file sizes** match expected values
4. **Test with a simple image workflow first** to ensure basic functionality

## Known Issues

- **Large video files**: Without S3 configured, videos are base64 encoded which can be very large
- **Memory requirements**: Wan video generation requires significant VRAM (8GB+ recommended)
- **Build timeouts**: Model downloads can take time; increase Docker build timeout if needed
