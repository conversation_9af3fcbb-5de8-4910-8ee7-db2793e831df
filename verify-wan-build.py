#!/usr/bin/env python3
"""
Comprehensive verification script for Wan video workflow build
Run this after building the Docker image to verify everything is working
"""
import os
import sys
import json
import requests
import time
from pathlib import Path

def check_file_exists(filepath, expected_size_mb=None):
    """Check if file exists and optionally verify size"""
    path = Path(filepath)
    if not path.exists():
        return False, f"File not found: {filepath}"
    
    size_mb = path.stat().st_size / (1024 * 1024)
    if expected_size_mb and abs(size_mb - expected_size_mb) > expected_size_mb * 0.1:  # 10% tolerance
        return False, f"File size mismatch: {size_mb:.1f}MB (expected ~{expected_size_mb}MB)"
    
    return True, f"✅ {filepath} ({size_mb:.1f}MB)"

def check_custom_nodes():
    """Verify custom nodes are installed"""
    print("🔍 Checking custom nodes installation...")
    
    nodes_dir = Path("/comfyui/custom_nodes")
    if not nodes_dir.exists():
        return False, "Custom nodes directory not found"
    
    required_nodes = [
        "ComfyUI-WanStartEndFramesNative",
        "ComfyUI-GGUF"
    ]
    
    missing_nodes = []
    for node in required_nodes:
        node_path = nodes_dir / node
        if not node_path.exists():
            missing_nodes.append(node)
        else:
            print(f"✅ {node} - Installed")
    
    if missing_nodes:
        return False, f"Missing custom nodes: {', '.join(missing_nodes)}"
    
    return True, "All custom nodes installed"

def check_models():
    """Verify all required models are downloaded"""
    print("🔍 Checking model files...")
    
    models = [
        ("/comfyui/models/unet/wan2.1-i2v-14b-480p-Q3_K_S.gguf", 7930),  # ~7.93GB
        ("/comfyui/models/vae/wan_2.1_vae.safetensors", 508),  # ~508MB
        ("/comfyui/models/clip_vision/clip_vision_h.safetensors", 2500),  # ~2.5GB
        ("/comfyui/models/clip/umt5_xxl_fp8_e4m3fn_scaled.safetensors", 4700),  # ~4.7GB
    ]
    
    all_good = True
    for filepath, expected_size in models:
        exists, message = check_file_exists(filepath, expected_size)
        print(message)
        if not exists:
            all_good = False
    
    return all_good, "Model verification complete"

def check_comfyui_api():
    """Check if ComfyUI API is responding"""
    print("🔍 Checking ComfyUI API...")
    
    try:
        response = requests.get("http://127.0.0.1:8188/", timeout=5)
        if response.status_code == 200:
            return True, "✅ ComfyUI API responding"
        else:
            return False, f"ComfyUI API returned status {response.status_code}"
    except requests.ConnectionError:
        return False, "❌ Cannot connect to ComfyUI API (not running?)"
    except Exception as e:
        return False, f"❌ Error checking ComfyUI API: {e}"

def check_wan_nodes_api():
    """Check if Wan nodes are available via API"""
    print("🔍 Checking Wan nodes via API...")
    
    try:
        response = requests.get("http://127.0.0.1:8188/object_info", timeout=10)
        response.raise_for_status()
        object_info = response.json()
        
        required_nodes = [
            "WanImageToVideo",
            "UnetLoaderGGUF", 
            "CLIPVisionLoader",
            "CLIPVisionEncode"
        ]
        
        missing_nodes = []
        for node in required_nodes:
            if node in object_info:
                print(f"✅ {node} - Available")
            else:
                missing_nodes.append(node)
                print(f"❌ {node} - Missing")
        
        if missing_nodes:
            return False, f"Missing nodes: {', '.join(missing_nodes)}"
        
        return True, "All required nodes available"
        
    except Exception as e:
        return False, f"Error checking nodes: {e}"

def test_workflow_validation():
    """Test workflow validation with a simple workflow"""
    print("🔍 Testing workflow validation...")
    
    # Simple test workflow
    test_workflow = {
        "56": {
            "inputs": {
                "unet_name": "wan2.1-i2v-14b-480p-Q3_K_S.gguf"
            },
            "class_type": "UnetLoaderGGUF"
        }
    }
    
    try:
        payload = {"prompt": test_workflow, "client_id": "test"}
        response = requests.post(
            "http://127.0.0.1:8188/prompt",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            return True, "✅ Workflow validation successful"
        elif response.status_code == 400:
            return False, f"❌ Workflow validation failed: {response.text}"
        else:
            return False, f"❌ Unexpected response: {response.status_code}"
            
    except Exception as e:
        return False, f"❌ Error testing workflow: {e}"

def main():
    """Run all verification checks"""
    print("🚀 Starting Wan Video Workflow Build Verification")
    print("=" * 60)
    
    checks = [
        ("Custom Nodes", check_custom_nodes),
        ("Model Files", check_models),
        ("ComfyUI API", check_comfyui_api),
        ("Wan Nodes API", check_wan_nodes_api),
        ("Workflow Validation", test_workflow_validation),
    ]
    
    all_passed = True
    results = []
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}")
        print("-" * 40)
        
        try:
            success, message = check_func()
            results.append((check_name, success, message))
            
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Error during {check_name}: {e}")
            results.append((check_name, False, str(e)))
            all_passed = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    for check_name, success, message in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status:8} {check_name}")
    
    print("-" * 60)
    
    if all_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("✅ Wan video workflow is ready to use")
        print("\nNext steps:")
        print("1. Deploy your Docker image to RunPod")
        print("2. Test with wan-test-input.json")
        print("3. Check docs/wan-video-troubleshooting.md if issues arise")
        return 0
    else:
        print("❌ SOME CHECKS FAILED!")
        print("🔧 Please fix the issues above before deploying")
        print("📖 See docs/wan-video-troubleshooting.md for help")
        return 1

if __name__ == "__main__":
    sys.exit(main())
