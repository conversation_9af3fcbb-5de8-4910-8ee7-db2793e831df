#0 building with "desktop-linux" instance using docker driver

#1 [internal] load build definition from Dockerfile.wan
#1 transferring dockerfile: 7.02kB done
#1 DONE 0.0s

#2 [internal] load metadata for docker.io/runpod/worker-comfyui:5.1.0-base
#2 ...

#3 [auth] runpod/worker-comfyui:pull token for registry-1.docker.io
#3 DONE 0.0s

#2 [internal] load metadata for docker.io/runpod/worker-comfyui:5.1.0-base
#2 DONE 2.3s

#4 [internal] load .dockerignore
#4 transferring context: 2B done
#4 DONE 0.0s

#5 [ 1/14] FROM docker.io/runpod/worker-comfyui:5.1.0-base@sha256:1e1b8fc1b2d763ac03da1073fab3d821d9e16f7079b7c46101ee87dd95489276
#5 resolve docker.io/runpod/worker-comfyui:5.1.0-base@sha256:1e1b8fc1b2d763ac03da1073fab3d821d9e16f7079b7c46101ee87dd95489276 0.0s done
#5 CACHED

#6 [internal] load build context
#6 transferring context: 12.65kB done
#6 DONE 0.0s

#7 [internal] preparing inline document
#7 DONE 0.0s

#8 [ 2/14] RUN apt-get update &&     apt-get install -y --no-install-recommends     libgl1     libglib2.0-0     libsm6     libxext6     libxrender1     ffmpeg     && apt-get clean &&     rm -rf /var/lib/apt/lists/*
#8 0.778 Get:1 https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2404/x86_64  InRelease [1581 B]
#8 0.936 Get:2 http://security.ubuntu.com/ubuntu noble-security InRelease [126 kB]
#8 1.139 Get:3 http://archive.ubuntu.com/ubuntu noble InRelease [256 kB]
#8 1.812 Get:4 http://security.ubuntu.com/ubuntu noble-security/universe amd64 Packages [1100 kB]
#8 2.084 Get:5 https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2404/x86_64  Packages [690 kB]
#8 2.129 Get:6 http://archive.ubuntu.com/ubuntu noble-updates InRelease [126 kB]
#8 2.374 Get:7 http://archive.ubuntu.com/ubuntu noble-backports InRelease [126 kB]
#8 2.620 Get:8 http://archive.ubuntu.com/ubuntu noble/multiverse amd64 Packages [331 kB]
#8 2.763 Get:9 http://security.ubuntu.com/ubuntu noble-security/restricted amd64 Packages [1442 kB]
#8 2.833 Get:10 http://archive.ubuntu.com/ubuntu noble/universe amd64 Packages [19.3 MB]
#8 3.003 Get:11 http://security.ubuntu.com/ubuntu noble-security/main amd64 Packages [1093 kB]
#8 3.131 Get:12 http://security.ubuntu.com/ubuntu noble-security/multiverse amd64 Packages [22.1 kB]
#8 5.230 Get:13 http://archive.ubuntu.com/ubuntu noble/restricted amd64 Packages [117 kB]
#8 5.380 Get:14 http://archive.ubuntu.com/ubuntu noble/main amd64 Packages [1808 kB]
#8 5.739 Get:15 http://archive.ubuntu.com/ubuntu noble-updates/universe amd64 Packages [1403 kB]
#8 6.344 Get:16 http://archive.ubuntu.com/ubuntu noble-updates/multiverse amd64 Packages [26.7 kB]
#8 6.344 Get:17 http://archive.ubuntu.com/ubuntu noble-updates/main amd64 Packages [1418 kB]
#8 6.375 Get:18 http://archive.ubuntu.com/ubuntu noble-updates/restricted amd64 Packages [1495 kB]
#8 6.527 Get:19 http://archive.ubuntu.com/ubuntu noble-backports/universe amd64 Packages [31.8 kB]
#8 6.530 Get:20 http://archive.ubuntu.com/ubuntu noble-backports/main amd64 Packages [48.0 kB]
#8 6.607 Fetched 31.0 MB in 6s (4970 kB/s)
#8 6.607 Reading package lists...
#8 7.388 W: https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2404/x86_64/InRelease: Key is stored in legacy trusted.gpg keyring (/etc/apt/trusted.gpg), see the DEPRECATION section in apt-key(8) for details.
#8 7.393 Reading package lists...
#8 8.129 Building dependency tree...
#8 8.302 Reading state information...
#8 8.570 libgl1 is already the newest version (1.7.0-1build1).
#8 8.570 libglib2.0-0t64 is already the newest version (2.80.0-6ubuntu3.4).
#8 8.570 libsm6 is already the newest version (2:1.2.3-1build3).
#8 8.570 libxext6 is already the newest version (2:1.3.4-1build2).
#8 8.570 libxrender1 is already the newest version (1:0.9.10-1.1build1).
#8 8.570 ffmpeg is already the newest version (7:6.1.1-3ubuntu5).
#8 8.570 0 upgraded, 0 newly installed, 0 to remove and 57 not upgraded.
#8 DONE 8.6s

#9 [ 3/14] RUN mkdir -p /comfyui/models/unet /comfyui/models/vae /comfyui/models/clip /comfyui/models/clip_vision
#9 DONE 0.5s

#10 [ 4/14] RUN echo "🔄 Installing Wan video custom nodes..." &&     comfy-node-install https://github.com/Flow-two/ComfyUI-WanStartEndFramesNative.git &&     comfy-node-install https://github.com/city96/ComfyUI-GGUF.git &&     echo "✅ Custom nodes installation completed"
#10 0.559 🔄 Installing Wan video custom nodes...
#10 1.793 Execute from: /comfyui
#10 145.6 FETCH ComfyRegistry Data: 5/88
#10 145.6 FETCH ComfyRegistry Data: 10/88
#10 145.6 FETCH ComfyRegistry Data: 15/88
#10 145.6 FETCH ComfyRegistry Data: 20/88
#10 145.6 FETCH ComfyRegistry Data: 25/88
#10 145.6 FETCH ComfyRegistry Data: 30/88
#10 145.6 FETCH ComfyRegistry Data: 35/88
#10 145.6 FETCH ComfyRegistry Data: 40/88
#10 145.6 FETCH ComfyRegistry Data: 45/88
#10 145.6 FETCH ComfyRegistry Data: 50/88
#10 145.6 FETCH ComfyRegistry Data: 55/88
#10 145.6 FETCH ComfyRegistry Data: 60/88
#10 145.6 FETCH ComfyRegistry Data: 65/88
#10 145.6 FETCH ComfyRegistry Data: 70/88
#10 145.6 FETCH ComfyRegistry Data: 75/88
#10 145.6 FETCH ComfyRegistry Data: 80/88
#10 145.6 FETCH ComfyRegistry Data: 85/88
#10 145.6 FETCH ComfyRegistry Data [DONE]
#10 145.6 FETCH DATA from: 
#10 145.6 https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list
#10 145.6 .json [DONE]
#10 145.6 install_node exit on fail:False...
#10 145.6 [ComfyUI-Manager] The ComfyRegistry cache update is still in progress, so an 
#10 145.6 outdated cache is being used.
#10 145.6 FETCH DATA from: 
#10 145.6 /comfyui/user/default/ComfyUI-Manager/cache/1514988643_custom-node-list.json 
#10 145.6 [DONE]
#10 145.6 Install: https://github.com/Flow-two/ComfyUI-WanStartEndFramesNative.git
#10 145.6 1/1 [INSTALLED] https://github.com/Flow-two/ComfyUI-WanStartEndFramesNative.git
#10 145.6 
#10 147.0 Execute from: /comfyui
#10 270.4 FETCH ComfyRegistry Data: 5/88
#10 270.4 FETCH ComfyRegistry Data: 10/88
#10 270.4 FETCH ComfyRegistry Data: 15/88
#10 270.4 FETCH ComfyRegistry Data: 20/88
#10 270.4 FETCH ComfyRegistry Data: 25/88
#10 270.4 FETCH ComfyRegistry Data: 30/88
#10 270.4 FETCH ComfyRegistry Data: 35/88
#10 270.4 FETCH ComfyRegistry Data: 40/88
#10 270.4 FETCH ComfyRegistry Data: 45/88
#10 270.4 FETCH ComfyRegistry Data: 50/88
#10 270.4 FETCH ComfyRegistry Data: 55/88
#10 270.4 FETCH ComfyRegistry Data: 60/88
#10 270.4 FETCH ComfyRegistry Data: 65/88
#10 270.4 FETCH ComfyRegistry Data: 70/88
#10 270.4 FETCH ComfyRegistry Data: 75/88
#10 270.4 FETCH ComfyRegistry Data: 80/88
#10 270.4 FETCH ComfyRegistry Data: 85/88
#10 270.4 FETCH ComfyRegistry Data [DONE]
#10 270.4 FETCH DATA from: 
#10 270.4 https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list
#10 270.4 .json [DONE]
#10 270.4 install_node exit on fail:False...
#10 270.4 [ComfyUI-Manager] The ComfyRegistry cache update is still in progress, so an 
#10 270.4 outdated cache is being used.
#10 270.4 FETCH DATA from: 
#10 270.4 /comfyui/user/default/ComfyUI-Manager/cache/1514988643_custom-node-list.json 
#10 270.4 [DONE]
#10 270.4 Install: https://github.com/city96/ComfyUI-GGUF.git
#10 270.4 Install: pip packages
#10 270.4 
#10 270.4 ## ComfyUI-Manager: EXECUTE => ['/opt/venv/bin/python', '-m', 'pip', 'install', 
#10 270.4 'gguf>=0.13.0']
#10 270.4 Collecting gguf>=0.13.0
#10 270.4   Downloading gguf-0.17.0-py3-none-any.whl.metadata (4.4 kB)
#10 270.4 Requirement already satisfied: numpy>=1.17 in 
#10 270.4 /opt/venv/lib/python3.12/site-packages (from gguf>=0.13.0) (1.26.4)
#10 270.4 Requirement already satisfied: pyyaml>=5.1 in 
#10 270.4 /opt/venv/lib/python3.12/site-packages (from gguf>=0.13.0) (6.0.2)
#10 270.4 Requirement already satisfied: sentencepiece<=0.2.0,>=0.1.98 in 
#10 270.4 /opt/venv/lib/python3.12/site-packages (from gguf>=0.13.0) (0.2.0)
#10 270.4 Requirement already satisfied: tqdm>=4.27 in 
#10 270.4 /opt/venv/lib/python3.12/site-packages (from gguf>=0.13.0) (4.67.1)
#10 270.4 Downloading gguf-0.17.0-py3-none-any.whl (95 kB)
#10 270.4 Installing collected packages: gguf
#10 270.4 Successfully installed gguf-0.17.0
#10 270.4 
#10 270.4 ## ComfyUI-Manager: EXECUTE => ['/opt/venv/bin/python', '-m', 'pip', 'install', 
#10 270.4 'sentencepiece']
#10 270.4 Requirement already satisfied: sentencepiece in 
#10 270.4 /opt/venv/lib/python3.12/site-packages (0.2.0)
#10 270.4 
#10 270.4 ## ComfyUI-Manager: EXECUTE => ['/opt/venv/bin/python', '-m', 'pip', 'install', 
#10 270.4 'protobuf']
#10 270.4 Collecting protobuf
#10 270.4   Downloading protobuf-6.31.1-cp39-abi3-manylinux2014_x86_64.whl.metadata (593 
#10 270.4 bytes)
#10 270.4 Downloading protobuf-6.31.1-cp39-abi3-manylinux2014_x86_64.whl (321 kB)
#10 270.4 Installing collected packages: protobuf
#10 270.4 Successfully installed protobuf-6.31.1
#10 270.4 1/1 [INSTALLED] https://github.com/city96/ComfyUI-GGUF.git        
#10 270.4 
#10 270.5 ✅ Custom nodes installation completed
#10 DONE 270.6s

#11 [ 5/14] RUN echo "🔄 Installing ComfyUI-VideoHelperSuite..." &&     comfy-node-install https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git &&     echo "� Verifying video processing dependencies..." &&     python -c "import cv2; print('✅ OpenCV imported successfully')" ||     (echo "⚠️  OpenCV not found, installing manually..." && pip install opencv-python-headless) &&     python -c "import imageio; print('✅ ImageIO imported successfully')" ||     (echo "⚠️  ImageIO not found, installing manually..." && pip install imageio imageio-ffmpeg) &&     echo "✅ ComfyUI-VideoHelperSuite installation completed"
#11 0.366 🔄 Installing ComfyUI-VideoHelperSuite...
#11 4.075 Execute from: /comfyui
#11 173.9 FETCH ComfyRegistry Data: 5/88
#11 173.9 FETCH ComfyRegistry Data: 10/88
#11 173.9 FETCH ComfyRegistry Data: 15/88
#11 173.9 FETCH ComfyRegistry Data: 20/88
#11 173.9 FETCH ComfyRegistry Data: 25/88
#11 173.9 FETCH ComfyRegistry Data: 30/88
#11 173.9 FETCH ComfyRegistry Data: 35/88
#11 173.9 FETCH ComfyRegistry Data: 40/88
#11 173.9 FETCH ComfyRegistry Data: 45/88
#11 173.9 FETCH ComfyRegistry Data: 50/88
#11 173.9 FETCH ComfyRegistry Data: 55/88
#11 173.9 FETCH ComfyRegistry Data: 60/88
#11 173.9 FETCH ComfyRegistry Data: 65/88
#11 173.9 FETCH ComfyRegistry Data: 70/88
#11 173.9 FETCH ComfyRegistry Data: 75/88
#11 173.9 FETCH ComfyRegistry Data: 80/88
#11 173.9 FETCH ComfyRegistry Data: 85/88
#11 173.9 FETCH ComfyRegistry Data [DONE]
#11 173.9 FETCH DATA from: 
#11 173.9 https://raw.githubusercontent.com/ltdrdata/ComfyUI-Manager/main/custom-node-list
#11 173.9 .json [DONE]
#11 173.9 install_node exit on fail:False...
#11 173.9 [ComfyUI-Manager] The ComfyRegistry cache update is still in progress, so an 
#11 173.9 outdated cache is being used.
#11 173.9 FETCH DATA from: 
#11 173.9 /comfyui/user/default/ComfyUI-Manager/cache/1514988643_custom-node-list.json 
#11 173.9 [DONE]
#11 173.9 Install: https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git
#11 173.9 Install: pip packages
#11 173.9 
#11 173.9 ## ComfyUI-Manager: EXECUTE => ['/opt/venv/bin/python', '-m', 'pip', 'install', 
#11 173.9 'opencv-python']
#11 173.9 Collecting opencv-python
#11 173.9   Downloading 
#11 173.9 opencv_python-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
#11 173.9 .metadata (20 kB)
#11 173.9 Requirement already satisfied: numpy>=1.21.2 in 
#11 173.9 /opt/venv/lib/python3.12/site-packages (from opencv-python) (1.26.4)
#11 173.9 Downloading 
#11 173.9 opencv_python-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
#11 173.9 (63.0 MB)
#11 173.9    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 63.0/63.0 MB 4.5 MB/s eta 0:00:00
#11 173.9 Installing collected packages: opencv-python
#11 173.9 Successfully installed opencv-python-*********
#11 173.9 
#11 173.9 ## ComfyUI-Manager: EXECUTE => ['/opt/venv/bin/python', '-m', 'pip', 'install', 
#11 173.9 'imageio-ffmpeg']
#11 173.9 Collecting imageio-ffmpeg
#11 173.9   Downloading imageio_ffmpeg-0.6.0-py3-none-manylinux2014_x86_64.whl.metadata 
#11 173.9 (1.5 kB)
#11 173.9 Downloading imageio_ffmpeg-0.6.0-py3-none-manylinux2014_x86_64.whl (29.5 MB)
#11 173.9    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 29.5/29.5 MB 3.9 MB/s eta 0:00:00
#11 173.9 Installing collected packages: imageio-ffmpeg
#11 173.9 Successfully installed imageio-ffmpeg-0.6.0
#11 173.9 1/1 [INSTALLED] https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git
#11 173.9 
#11 174.0 � Verifying video processing dependencies...
#11 174.2 ✅ OpenCV imported successfully
#11 174.2 Traceback (most recent call last):
#11 174.2   File "<string>", line 1, in <module>
#11 174.2 ModuleNotFoundError: No module named 'imageio'
#11 174.2 ⚠️  ImageIO not found, installing manually...
#11 174.7 Collecting imageio
#11 174.8   Downloading imageio-2.37.0-py3-none-any.whl.metadata (5.2 kB)
#11 174.8 Requirement already satisfied: imageio-ffmpeg in /opt/venv/lib/python3.12/site-packages (0.6.0)
#11 174.8 Requirement already satisfied: numpy in /opt/venv/lib/python3.12/site-packages (from imageio) (1.26.4)
#11 174.8 Requirement already satisfied: pillow>=8.3.2 in /opt/venv/lib/python3.12/site-packages (from imageio) (11.2.1)
#11 174.8 Downloading imageio-2.37.0-py3-none-any.whl (315 kB)
#11 175.1 Installing collected packages: imageio
#11 175.3 Successfully installed imageio-2.37.0
#11 175.4 ✅ ComfyUI-VideoHelperSuite installation completed
#11 DONE 175.6s

#12 [ 6/14] COPY check-wan-nodes.py verify-wan-build.py verify-custom-nodes.py diagnose-vhs-runtime.py ./
#12 DONE 0.1s

#13 [ 7/14] RUN chmod +x check-wan-nodes.py verify-wan-build.py verify-custom-nodes.py diagnose-vhs-runtime.py
#13 DONE 0.5s

#14 [ 8/14] COPY <<EOF /verify-vhs-nodes.py
#14 DONE 0.0s

#15 [ 9/14] RUN chmod +x /verify-vhs-nodes.py && python3 /verify-vhs-nodes.py
#15 0.427 🔍 Verifying VideoHelperSuite installation...
#15 0.427 ❌ VideoHelperSuite directory not found: /comfyui/custom_nodes/ComfyUI-VideoHelperSuite
#15 ERROR: process "/bin/sh -c chmod +x /verify-vhs-nodes.py && python3 /verify-vhs-nodes.py" did not complete successfully: exit code: 1
------
 > [ 9/14] RUN chmod +x /verify-vhs-nodes.py && python3 /verify-vhs-nodes.py:
0.427 🔍 Verifying VideoHelperSuite installation...
0.427 ❌ VideoHelperSuite directory not found: /comfyui/custom_nodes/ComfyUI-VideoHelperSuite
------
Dockerfile.wan:97
--------------------
  95 |     EOF
  96 |     
  97 | >>> RUN chmod +x /verify-vhs-nodes.py && python3 /verify-vhs-nodes.py
  98 |     
  99 |     # Verify custom nodes were installed successfully
--------------------
ERROR: failed to solve: process "/bin/sh -c chmod +x /verify-vhs-nodes.py && python3 /verify-vhs-nodes.py" did not complete successfully: exit code: 1

View build details: docker-desktop://dashboard/build/desktop-linux/desktop-linux/9mprhudcei9y49f0yjrr99caz
