#!/usr/bin/env python3
"""
Runtime diagnostic script for VideoHelperSuite issues in RunPod serverless
Run this script inside the container to diagnose VHS loading problems
"""
import os
import sys
import subprocess
import importlib.util
import requests
import json

def check_system_dependencies():
    """Check if required system dependencies are available"""
    print("🔍 Checking system dependencies...")
    
    # Check ffmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ ffmpeg is available")
            # Get version info
            version_line = result.stdout.split('\n')[0]
            print(f"   Version: {version_line}")
        else:
            print("❌ ffmpeg not working properly")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ ffmpeg not found or not working")
    
    # Check Python dependencies
    print("\n🔍 Checking Python dependencies...")
    required_packages = ['cv2', 'imageio', 'numpy', 'PIL']
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} imported successfully")
        except ImportError as e:
            print(f"❌ {package} import failed: {e}")

def check_vhs_files():
    """Check VideoHelperSuite file structure"""
    print("\n🔍 Checking VideoHelperSuite file structure...")
    
    vhs_path = "/comfyui/custom_nodes/ComfyUI-VideoHelperSuite"
    
    if not os.path.exists(vhs_path):
        print(f"❌ VideoHelperSuite directory not found: {vhs_path}")
        return False
    
    print(f"✅ VideoHelperSuite directory found: {vhs_path}")
    
    # List all Python files
    python_files = []
    for root, dirs, files in os.walk(vhs_path):
        for file in files:
            if file.endswith('.py'):
                rel_path = os.path.relpath(os.path.join(root, file), vhs_path)
                python_files.append(rel_path)
    
    print(f"📁 Found {len(python_files)} Python files:")
    for file in python_files[:10]:  # Show first 10
        print(f"   {file}")
    if len(python_files) > 10:
        print(f"   ... and {len(python_files) - 10} more")
    
    # Check for key files
    key_files = ['__init__.py', 'nodes.py']
    for file in key_files:
        file_path = os.path.join(vhs_path, file)
        if os.path.exists(file_path):
            print(f"✅ Found {file}")
            # Check file size
            size = os.path.getsize(file_path)
            print(f"   Size: {size} bytes")
        else:
            print(f"⚠️  Missing {file}")
    
    # Search for VHS_VideoCombine
    print("\n🔍 Searching for VHS_VideoCombine definition...")
    found_vhs_combine = False
    
    for root, dirs, files in os.walk(vhs_path):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if 'VHS_VideoCombine' in content:
                            rel_path = os.path.relpath(file_path, vhs_path)
                            print(f"✅ Found VHS_VideoCombine in {rel_path}")
                            found_vhs_combine = True
                            
                            # Check for class definition
                            if 'class VHS_VideoCombine' in content:
                                print("   ✅ Found class definition")
                            else:
                                print("   ⚠️  Found reference but not class definition")
                except Exception as e:
                    continue
    
    if not found_vhs_combine:
        print("❌ VHS_VideoCombine not found in any files")
    
    return found_vhs_combine

def check_comfyui_import():
    """Check if ComfyUI can import VideoHelperSuite"""
    print("\n🔍 Testing ComfyUI import of VideoHelperSuite...")
    
    # Add ComfyUI to Python path
    comfyui_path = "/comfyui"
    if comfyui_path not in sys.path:
        sys.path.insert(0, comfyui_path)
    
    vhs_path = "/comfyui/custom_nodes/ComfyUI-VideoHelperSuite"
    if vhs_path not in sys.path:
        sys.path.insert(0, vhs_path)
    
    try:
        # Try to import the VideoHelperSuite module
        spec = importlib.util.spec_from_file_location(
            "videohelpersuite", 
            os.path.join(vhs_path, "__init__.py")
        )
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            print("✅ VideoHelperSuite module imported successfully")
            return True
        else:
            print("❌ Could not create module spec for VideoHelperSuite")
    except Exception as e:
        print(f"❌ Failed to import VideoHelperSuite: {e}")
        print(f"   Error type: {type(e).__name__}")
    
    return False

def check_comfyui_api():
    """Check ComfyUI API for available nodes"""
    print("\n🔍 Checking ComfyUI API for VideoHelperSuite nodes...")
    
    try:
        response = requests.get("http://127.0.0.1:8188/object_info", timeout=10)
        response.raise_for_status()
        object_info = response.json()
        
        # Look for VHS nodes
        vhs_nodes = []
        for node_name in object_info.keys():
            if 'vhs' in node_name.lower() or 'video' in node_name.lower():
                vhs_nodes.append(node_name)
        
        if vhs_nodes:
            print(f"✅ Found {len(vhs_nodes)} VideoHelperSuite nodes:")
            for node in vhs_nodes:
                print(f"   {node}")
        else:
            print("❌ No VideoHelperSuite nodes found in ComfyUI API")
        
        # Specifically check for VHS_VideoCombine
        if "VHS_VideoCombine" in object_info:
            print("✅ VHS_VideoCombine node is available!")
            return True
        else:
            print("❌ VHS_VideoCombine node not found in API")
            return False
            
    except requests.ConnectionError:
        print("❌ Cannot connect to ComfyUI API at http://127.0.0.1:8188")
        print("   Make sure ComfyUI is running")
        return False
    except Exception as e:
        print(f"❌ Error checking ComfyUI API: {e}")
        return False

def main():
    """Run all diagnostic checks"""
    print("🚀 VideoHelperSuite Runtime Diagnostics")
    print("=" * 50)
    
    # Run all checks
    checks = [
        ("System Dependencies", check_system_dependencies),
        ("VHS File Structure", check_vhs_files),
        ("Python Import", check_comfyui_import),
        ("ComfyUI API", check_comfyui_api)
    ]
    
    results = {}
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ {check_name} check failed with exception: {e}")
            results[check_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("📊 DIAGNOSTIC SUMMARY")
    print(f"{'='*50}")
    
    for check_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check_name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 All checks passed! VideoHelperSuite should be working.")
    else:
        print("\n⚠️  Some checks failed. VideoHelperSuite may not work properly.")
        print("\n🔧 Troubleshooting suggestions:")
        print("1. Restart ComfyUI completely")
        print("2. Check ComfyUI console logs for import errors")
        print("3. Verify all dependencies are installed")
        print("4. Try reinstalling VideoHelperSuite")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
