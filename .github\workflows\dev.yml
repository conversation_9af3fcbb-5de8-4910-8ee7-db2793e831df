name: Development

on:
  workflow_dispatch:
#  push:
#    branches-ignore:
#      - main

jobs:
  dev:
    runs-on: [blacksmith-8vcpu-ubuntu-2204, linux]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Clear space to remove unused folders
        run: |
          rm -rf /usr/share/dotnet
          rm -rf /opt/ghc
          rm -rf "/usr/local/share/boost"
          rm -rf "$AGENT_TOOLSDIRECTORY"

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: blacksmith docker layer cache
        uses: useblacksmith/build-push-action@v1
        with:
          setup-only: true

      - name: Set environment variables
        run: |
          echo "DOCKERHUB_REPO=${{ vars.DOCKERHUB_REPO }}" >> $GITHUB_ENV
          echo "DOCKERHUB_IMG=${{ vars.DOCKERHUB_IMG }}" >> $GITHUB_ENV
          echo "HUGGINGFACE_ACCESS_TOKEN=${{ secrets.HUGGINGFACE_ACCESS_TOKEN }}" >> $GITHUB_ENV
          echo "RELEASE_VERSION=${GITHUB_REF##refs/heads/}" | sed 's/\//-/g' >> $GITHUB_ENV

      - name: Build and push the images to Docker Hub
        uses: docker/bake-action@v2
        with:
          push: true
          set: |
            *.args.DOCKERHUB_REPO=${{ env.DOCKERHUB_REPO }}
            *.args.DOCKERHUB_IMG=${{ env.DOCKERHUB_IMG }}
            *.args.RELEASE_VERSION=${{ env.RELEASE_VERSION }}
            *.args.HUGGINGFACE_ACCESS_TOKEN=${{ env.HUGGINGFACE_ACCESS_TOKEN }}
