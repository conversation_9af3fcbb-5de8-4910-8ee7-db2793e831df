#!/usr/bin/env python3
"""
Check if video encoding nodes are available in ComfyUI
"""
import requests
import json

def check_video_nodes():
    """Check if video encoding nodes are available"""

    try:
        # Get object info from ComfyUI
        print("Connecting to ComfyUI API...")
        response = requests.get("http://127.0.0.1:8188/object_info", timeout=10)
        response.raise_for_status()
        object_info = response.json()

        # Video encoding nodes to check
        video_nodes = [
            "VHS_VideoCombine",
            "SaveAnimatedWEBP", 
            "SaveAnimatedPNG",
            "VideoLinearCFGGuidance"
        ]

        print("\n🎥 Checking video encoding nodes...")
        available_nodes = []
        missing_nodes = []

        for node in video_nodes:
            if node in object_info:
                print(f"✅ {node} - Available")
                available_nodes.append(node)
            else:
                print(f"❌ {node} - Missing")
                missing_nodes.append(node)

        print(f"\n📊 Summary:")
        print(f"   Available: {len(available_nodes)}")
        print(f"   Missing: {len(missing_nodes)}")

        if "VHS_VideoCombine" not in available_nodes:
            print(f"\n❌ VHS_VideoCombine is required for MP4 video output!")
            print("You need to install VideoHelperSuite:")
            print("   comfy-node-install https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git")
            return False
        else:
            print(f"\n✅ Video encoding nodes are available!")
            print("✅ MP4 video output should work!")
            return True

    except requests.exceptions.RequestException as e:
        print(f"❌ Error connecting to ComfyUI: {e}")
        print("Make sure ComfyUI is running on http://127.0.0.1:8188")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    check_video_nodes()
