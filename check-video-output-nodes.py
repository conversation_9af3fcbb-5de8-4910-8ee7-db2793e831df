#!/usr/bin/env python3
"""
Check what video output nodes are available in your ComfyUI setup
"""
import requests
import json

def check_video_nodes():
    """Check available video output nodes"""
    try:
        print("🔍 Checking available video output nodes...")
        response = requests.get("http://127.0.0.1:8188/object_info", timeout=10)
        response.raise_for_status()
        object_info = response.json()

        # Video output nodes to look for
        video_output_nodes = [
            "VHS_VideoCombine",           # VideoHelperSuite - MP4 output
            "SaveAnimatedWEBP",           # Animated WEBP
            "SaveAnimatedPNG",            # Animated PNG  
            "SaveVideo",                  # Generic video save
            "VideoSave",                  # Alternative video save
            "AnimateDiffVideoSave",       # AnimateDiff video output
            "SVD_img2vid_Conditioning",   # Stable Video Diffusion
            "ImageSequenceToVideo",       # Image sequence to video
            "FramesToVideo",              # Frames to video conversion
        ]

        print(f"\n📋 Scanning {len(video_output_nodes)} potential video output nodes...")
        
        available = []
        missing = []
        
        for node in video_output_nodes:
            if node in object_info:
                available.append(node)
                print(f"✅ {node} - Available")
                
                # Show inputs for available nodes
                if 'input' in object_info[node]:
                    inputs = object_info[node]['input']['required']
                    print(f"   📝 Inputs: {list(inputs.keys())}")
            else:
                missing.append(node)
                print(f"❌ {node} - Missing")

        print(f"\n📊 Summary:")
        print(f"   ✅ Available: {len(available)}")
        print(f"   ❌ Missing: {len(missing)}")

        if available:
            print(f"\n🎉 You can use these nodes for video output:")
            for node in available:
                print(f"   - {node}")
            return True, available
        else:
            print(f"\n❌ No video output nodes found!")
            print(f"💡 You need to install VideoHelperSuite:")
            print(f"   git clone https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git")
            print(f"   # Or use ComfyUI Manager to install it")
            return False, []

    except Exception as e:
        print(f"❌ Error checking nodes: {e}")
        return False, []

def check_all_nodes_with_video():
    """Check all nodes that might handle video"""
    try:
        response = requests.get("http://127.0.0.1:8188/object_info", timeout=10)
        response.raise_for_status()
        object_info = response.json()

        print(f"\n🔍 Searching all nodes for video-related functionality...")
        
        video_related = []
        for node_name, node_info in object_info.items():
            # Look for video-related keywords in node names
            if any(keyword in node_name.lower() for keyword in ['video', 'animate', 'gif', 'webp', 'mp4', 'save', 'export']):
                video_related.append(node_name)
        
        if video_related:
            print(f"🎬 Found {len(video_related)} potentially video-related nodes:")
            for node in sorted(video_related)[:20]:  # Show first 20
                print(f"   - {node}")
            if len(video_related) > 20:
                print(f"   ... and {len(video_related) - 20} more")
        else:
            print(f"❌ No video-related nodes found")
            
        return video_related

    except Exception as e:
        print(f"❌ Error scanning nodes: {e}")
        return []

if __name__ == "__main__":
    print("🎥 Video Output Node Checker")
    print("=" * 50)
    
    # Check specific video nodes
    success, available_nodes = check_video_nodes()
    
    # Check all video-related nodes
    all_video_nodes = check_all_nodes_with_video()
    
    print(f"\n🎯 Recommendations:")
    if success and available_nodes:
        print(f"✅ Use one of the available video nodes: {available_nodes[0]}")
        print(f"✅ Modify your workflow to replace SaveImage with a video output node")
    else:
        print(f"❌ Install VideoHelperSuite for proper video output")
        print(f"❌ Current workflow will only output individual frames")
    
    print(f"\n📝 Next steps:")
    print(f"1. Install missing video nodes if needed")
    print(f"2. Create new workflow with video output node")
    print(f"3. Test complete video generation")
