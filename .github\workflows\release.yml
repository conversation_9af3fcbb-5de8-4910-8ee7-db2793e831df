name: release

on:
  workflow_dispatch:
  push:
    branches:
      - "main"

jobs:
  release:
    runs-on: [blacksmith-8vcpu-ubuntu-2204, linux]
    if: ${{ !contains(github.event.head_commit.message, 'chore(release)') }}
    permissions:
      contents: write
      issues: write
      pull-requests: write

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Clear space to remove unused folders
        run: |
          rm -rf /usr/share/dotnet
          rm -rf /opt/ghc
          rm -rf "/usr/local/share/boost"
          rm -rf "$AGENT_TOOLSDIRECTORY"

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Update the ToC in the README.md
        run: npx markdown-toc README.md -i

      - name: Semantic release
        uses: codfish/semantic-release-action@v3
        id: semanticrelease
        with:
          additional-packages: |
            ['@semantic-release/git', '@semantic-release/changelog', '@semantic-release/exec']
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}

      - name: Set environment variables
        if: steps.semanticrelease.outputs.new-release-published == 'true'
        run: |
          echo "DOCKERHUB_REPO=${{ vars.DOCKERHUB_REPO }}" >> $GITHUB_ENV
          echo "DOCKERHUB_IMG=${{ vars.DOCKERHUB_IMG }}" >> $GITHUB_ENV
          echo "HUGGINGFACE_ACCESS_TOKEN=${{ secrets.HUGGINGFACE_ACCESS_TOKEN }}" >> $GITHUB_ENV
          echo "RELEASE_VERSION=${{ steps.semanticrelease.outputs.release-version }}" >> $GITHUB_ENV

      - name: Build and push the images to Docker Hub
        if: steps.semanticrelease.outputs.new-release-published == 'true'
        uses: docker/bake-action@v2
        with:
          push: true
          set: |
            *.args.DOCKERHUB_REPO=${{ env.DOCKERHUB_REPO }}
            *.args.DOCKERHUB_IMG=${{ env.DOCKERHUB_IMG }}
            *.args.RELEASE_VERSION=${{ env.RELEASE_VERSION }}
            *.args.HUGGINGFACE_ACCESS_TOKEN=${{ env.HUGGINGFACE_ACCESS_TOKEN }}

      - name: Update description on Docker Hub
        if: steps.semanticrelease.outputs.new-release-published == 'true'
        uses: peter-evans/dockerhub-description@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
          repository: ${{ env.DOCKERHUB_REPO }}/${{ env.DOCKERHUB_IMG }}
