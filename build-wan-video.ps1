# PowerShell build script for Wan Video Docker image
param(
    [string]$ImageName = "vanna88/wan-video",
    [string]$Tag = "latest",
    [switch]$Push = $false
)

Write-Host "🚀 Building Wan Video Docker Image with Enhanced VHS Support" -ForegroundColor Blue
Write-Host "============================================================" -ForegroundColor Blue

$Dockerfile = "Dockerfile.wan"

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker and try again." -ForegroundColor Red
    exit 1
}

# Check if Dockerfile exists
if (-not (Test-Path $Dockerfile)) {
    Write-Host "❌ Dockerfile '$Dockerfile' not found in current directory" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Found $Dockerfile" -ForegroundColor Green

# Check required files
$RequiredFiles = @(
    "handler.py",
    "check-wan-nodes.py", 
    "verify-wan-build.py",
    "verify-custom-nodes.py",
    "diagnose-vhs-runtime.py"
)

foreach ($file in $RequiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "❌ Required file '$file' not found" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ All required files found" -ForegroundColor Green

# Build the Docker image
Write-Host "🔄 Building Docker image: $ImageName`:$Tag" -ForegroundColor Yellow
Write-Host "This may take 15-30 minutes due to large model downloads..." -ForegroundColor Yellow

$BuildCommand = "docker build --no-cache --progress=plain -f $Dockerfile -t $ImageName`:$Tag ."

try {
    Invoke-Expression $BuildCommand
    Write-Host "✅ Docker image built successfully: $ImageName`:$Tag" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker build failed: $_" -ForegroundColor Red
    exit 1
}

# Get image size
$ImageSize = docker images "$ImageName`:$Tag" --format "table {{.Size}}" | Select-Object -Last 1
Write-Host "📊 Image size: $ImageSize" -ForegroundColor Cyan

# Test the image
Write-Host "🧪 Testing image with quick verification..." -ForegroundColor Yellow

try {
    docker run --rm "$ImageName`:$Tag" python3 /verify-vhs-nodes.py
    Write-Host "✅ VHS verification test passed" -ForegroundColor Green
} catch {
    Write-Host "⚠️  VHS verification test failed, but image was built" -ForegroundColor Yellow
}

Write-Host "✅ Build completed successfully!" -ForegroundColor Green

# Push to Docker Hub if requested
if ($Push) {
    Write-Host "🚀 Pushing image to Docker Hub..." -ForegroundColor Blue
    
    try {
        docker push "$ImageName`:$Tag"
        Write-Host "✅ Image pushed successfully to Docker Hub: $ImageName`:$Tag" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to push image to Docker Hub: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host ""
    $pushChoice = Read-Host "Do you want to push the image to Docker Hub? (y/N)"
    if ($pushChoice -eq "y" -or $pushChoice -eq "Y") {
        Write-Host "🚀 Pushing image to Docker Hub..." -ForegroundColor Blue
        
        try {
            docker push "$ImageName`:$Tag"
            Write-Host "✅ Image pushed successfully to Docker Hub: $ImageName`:$Tag" -ForegroundColor Green
        } catch {
            Write-Host "❌ Failed to push image to Docker Hub: $_" -ForegroundColor Red
        }
    }
}

# Usage instructions
Write-Host ""
Write-Host "✅ Build completed! Here's how to use your image:" -ForegroundColor Green
Write-Host ""
Write-Host "🐳 Local testing with Docker Compose:" -ForegroundColor Cyan
Write-Host "   docker-compose -f docker-compose.wan.yml up"
Write-Host ""
Write-Host "🚀 Deploy to RunPod:" -ForegroundColor Cyan
Write-Host "   1. Use image: $ImageName`:$Tag"
Write-Host "   2. Set environment variables as needed"
Write-Host "   3. Use wan-video-proper.json workflow for testing"
Write-Host ""
Write-Host "🔍 Debugging commands:" -ForegroundColor Cyan
Write-Host "   # Check VHS nodes availability:"
Write-Host "   docker run --rm $ImageName`:$Tag python3 /check-wan-nodes.py"
Write-Host ""
Write-Host "   # Run full diagnostics:"
Write-Host "   docker run --rm $ImageName`:$Tag python3 /diagnose-vhs-runtime.py"
Write-Host ""
Write-Host "   # Interactive debugging:"
Write-Host "   docker run -it --rm $ImageName`:$Tag /bin/bash"
Write-Host ""
Write-Host "🎉 All done!" -ForegroundColor Green
