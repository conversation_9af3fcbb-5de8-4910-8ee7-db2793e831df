services:
  wan-video:
    build:
      context: .
      dockerfile: Dockerfile.wan
      platforms:
        - linux/amd64
    image: wan-video:latest
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    environment:
      - SERVE_API_LOCALLY=true
      - COMFY_LOG_LEVEL=DEBUG
      - REFRESH_WORKER=false # Disable for local testing
      - WEBSOCKET_RECONNECT_ATTEMPTS=10
      - WEBSOCKET_RECONNECT_DELAY_S=5
      # Add S3 credentials if testing S3 upload
      # - BUCKET_ENDPOINT_URL=https://your-bucket.s3.region.amazonaws.com
      # - BUCKET_ACCESS_KEY_ID=your-key
      # - BUCKET_SECRET_ACCESS_KEY=your-secret
    ports:
      - "8000:8000" # RunPod API simulation
      - "8188:8188" # ComfyUI direct access
    volumes:
      - ./data/comfyui/output:/comfyui/output
      - ./data/runpod-volume:/runpod-volume
      # Mount test workflows for easy access
      - ./test_resources:/test_resources:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8188/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
