#!/bin/bash
set -e

# Build script for Wan Video Docker image with enhanced debugging
echo "🚀 Building Wan Video Docker Image with Enhanced VHS Support"
echo "============================================================"

# Configuration
IMAGE_NAME="vanna88/wan-video"
TAG="latest"
DOCKERFILE="Dockerfile.wan"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_success "Docker is running"

# Check if Dockerfile exists
if [ ! -f "$DOCKERFILE" ]; then
    print_error "Dockerfile '$DOCKERFILE' not found in current directory"
    exit 1
fi

print_success "Found $DOCKERFILE"

# Check if required files exist
required_files=(
    "handler.py"
    "check-wan-nodes.py"
    "verify-wan-build.py"
    "verify-custom-nodes.py"
    "diagnose-vhs-runtime.py"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Required file '$file' not found"
        exit 1
    fi
done

print_success "All required files found"

# Build the Docker image
print_status "Building Docker image: $IMAGE_NAME:$TAG"
print_status "This may take 15-30 minutes due to large model downloads..."

# Build with verbose output and no cache to ensure fresh build
# Include --platform linux/amd64 for RunPod compatibility as per documentation
docker build \
    --platform linux/amd64 \
    --no-cache \
    --progress=plain \
    -f "$DOCKERFILE" \
    -t "$IMAGE_NAME:$TAG" \
    . 2>&1 | tee build.log

# Check if build was successful
if [ ${PIPESTATUS[0]} -eq 0 ]; then
    print_success "Docker image built successfully: $IMAGE_NAME:$TAG"
else
    print_error "Docker build failed. Check build.log for details."
    exit 1
fi

# Get image size
IMAGE_SIZE=$(docker images "$IMAGE_NAME:$TAG" --format "table {{.Size}}" | tail -n 1)
print_status "Image size: $IMAGE_SIZE"

# Test the image with a quick verification
print_status "Testing image with quick verification..."

# Run a quick test to verify the image works
docker run --rm "$IMAGE_NAME:$TAG" python3 /verify-vhs-nodes.py || {
    print_warning "VHS verification test failed, but image was built"
}

print_success "Build completed successfully!"

# Ask if user wants to push to Docker Hub
echo ""
read -p "Do you want to push the image to Docker Hub? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Pushing image to Docker Hub..."
    
    # Check if logged in to Docker Hub
    if ! docker info | grep -q "Username:"; then
        print_warning "Not logged in to Docker Hub. Please login first:"
        docker login
    fi
    
    # Push the image
    docker push "$IMAGE_NAME:$TAG"
    
    if [ $? -eq 0 ]; then
        print_success "Image pushed successfully to Docker Hub: $IMAGE_NAME:$TAG"
    else
        print_error "Failed to push image to Docker Hub"
        exit 1
    fi
else
    print_status "Skipping Docker Hub push"
fi

# Provide usage instructions
echo ""
print_success "Build completed! Here's how to use your image:"
echo ""
echo "🐳 Local testing with Docker Compose:"
echo "   docker-compose -f docker-compose.wan.yml up"
echo ""
echo "🚀 Deploy to RunPod:"
echo "   1. Use image: $IMAGE_NAME:$TAG"
echo "   2. Set environment variables as needed"
echo "   3. Use wan-video-proper.json workflow for testing"
echo ""
echo "🔍 Debugging commands:"
echo "   # Check VHS nodes availability:"
echo "   docker run --rm $IMAGE_NAME:$TAG python3 /check-wan-nodes.py"
echo ""
echo "   # Run full diagnostics:"
echo "   docker run --rm $IMAGE_NAME:$TAG python3 /diagnose-vhs-runtime.py"
echo ""
echo "   # Interactive debugging:"
echo "   docker run -it --rm $IMAGE_NAME:$TAG /bin/bash"
echo ""

print_success "All done! 🎉"
