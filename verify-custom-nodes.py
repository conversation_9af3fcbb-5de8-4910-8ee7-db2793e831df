#!/usr/bin/env python3
"""
Simple script to verify custom nodes are installed correctly during Docker build
"""
import sys
import os
from pathlib import Path

def main():
    print("🔍 Verifying custom nodes installation...")
    
    # Add ComfyUI to path
    sys.path.append('/comfyui')
    
    # Check if custom nodes directories exist
    custom_nodes_dir = Path('/comfyui/custom_nodes')
    if not custom_nodes_dir.exists():
        print("❌ Custom nodes directory not found")
        return 1
    
    required_nodes = [
        'ComfyUI-WanStartEndFramesNative',
        'ComfyUI-GGUF',
        'ComfyUI-VideoHelperSuite'  # Added check for VideoHelperSuite
    ]
    
    missing_nodes = []
    for node in required_nodes:
        node_path = custom_nodes_dir / node
        if not node_path.exists():
            missing_nodes.append(node)
            print(f"❌ {node} directory not found")
        else:
            print(f"✅ {node} directory found")
    
    if missing_nodes:
        print(f"❌ Missing custom nodes: {', '.join(missing_nodes)}")
        return 1
    
    # Check for key files that indicate successful installation
    print("🔍 Checking for key custom node files...")

    # Check ComfyUI-WanStartEndFramesNative files
    wan_files = [
        custom_nodes_dir / 'ComfyUI-WanStartEndFramesNative' / '__init__.py',
        custom_nodes_dir / 'ComfyUI-WanStartEndFramesNative' / 'nodes',
    ]

    for file_path in wan_files:
        if not file_path.exists():
            print(f"❌ Missing key file: {file_path}")
            return 1
        else:
            print(f"✅ Found: {file_path.name}")

    # Check ComfyUI-GGUF files
    gguf_files = [
        custom_nodes_dir / 'ComfyUI-GGUF' / '__init__.py',
        custom_nodes_dir / 'ComfyUI-GGUF' / 'nodes.py',
    ]

    for file_path in gguf_files:
        if not file_path.exists():
            print(f"❌ Missing key file: {file_path}")
            return 1
        else:
            print(f"✅ Found: {file_path.name}")

    # Check ComfyUI-VideoHelperSuite files
    vhs_files = [
        custom_nodes_dir / 'ComfyUI-VideoHelperSuite' / '__init__.py',
        # You might want to add a specific node file if you know one, e.g.:
        # custom_nodes_dir / 'ComfyUI-VideoHelperSuite' / 'VHS_video_nodes.py', 
    ]

    for file_path in vhs_files:
        if not file_path.exists():
            print(f"❌ Missing key file for VideoHelperSuite: {file_path}")
            return 1
        else:
            print(f"✅ Found for VideoHelperSuite: {file_path.name}")

    print("🎉 All custom nodes verified successfully!")
    print("ℹ️  Import testing will be done when ComfyUI starts")
    return 0

if __name__ == "__main__":
    sys.exit(main())
