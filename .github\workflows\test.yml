name: Tests

on:
  workflow_dispatch:
#  push:
#    branches: [main, dev]
#  pull_request:
#    types: [opened, synchronize]
#    branches: [main, dev]

jobs:
  test:
    runs-on: [blacksmith-8vcpu-ubuntu-2204, linux]

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Python
        uses: useblacksmith/setup-python@v6
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: pip install -r requirements.txt

      - name: Run Python tests
        run: python -m unittest discover
