# Contributing

When contributing to this repository, please first discuss the change you wish to make via issue,
email, or any other method with the owners of this repository before making a change.

Please note we have a code of conduct, please follow it in all your interactions with the project.

## Pull Request Process

Ensure any install or build dependencies are removed before the end of the layer when doing a build.
Fork the repository and create a new branch (feature/my-feature) Commit changes following the
"conventional-changelog" rules. Do not modify any versions manually. Don't build new versions. Use
the PULL_REQUEST_TEMPLATE

## Reporting issues

Ensure any install or build dependencies are removed before the end of the layer when doing a build.
Create a new issue (bug/some-bug) Always list "yarn version", "node version" Use the ISSUE_TEMPLATE
