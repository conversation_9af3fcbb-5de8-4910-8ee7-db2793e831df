#!/usr/bin/env python3
"""
Check if Wan video nodes are available in ComfyUI
"""
import requests
import json

def check_wan_nodes():
    """Check if Wan video generation nodes are available"""

    try:
        # Get object info from ComfyUI
        print("Connecting to ComfyUI API...")
        response = requests.get("http://127.0.0.1:8188/object_info", timeout=10)
        response.raise_for_status()
        object_info = response.json()

        # Required nodes for Wan video generation
        required_nodes = [
            "WanImageToVideo",
            "UnetLoaderGGUF",
            "CLIPVisionLoader",
            "CLIPVisionEncode"
        ]

        print("Checking for Wan video nodes...")
        print("=" * 50)

        missing_nodes = []
        available_nodes = []

        for node in required_nodes:
            if node in object_info:
                available_nodes.append(node)
                print(f"✅ {node} - Available")
                # Show node details for debugging
                node_info = object_info[node]
                if "input" in node_info and "required" in node_info["input"]:
                    inputs = list(node_info["input"]["required"].keys())
                    print(f"   Inputs: {', '.join(inputs[:3])}{'...' if len(inputs) > 3 else ''}")
            else:
                missing_nodes.append(node)
                print(f"❌ {node} - Missing")

        print("=" * 50)

        # Additional diagnostics
        print(f"\nDiagnostics:")
        print(f"Total nodes available: {len(object_info)}")

        # Check for related nodes that might indicate partial installation
        related_nodes = []
        for node_name in object_info.keys():
            if any(keyword in node_name.lower() for keyword in ['wan', 'gguf', 'vision']):
                related_nodes.append(node_name)

        if related_nodes:
            print(f"Related nodes found: {', '.join(related_nodes[:5])}{'...' if len(related_nodes) > 5 else ''}")

        if missing_nodes:
            print(f"\n❌ Missing {len(missing_nodes)} required nodes:")
            for node in missing_nodes:
                print(f"   - {node}")
            print("\nYou need to install the Wan video custom nodes:")
            print("   - ComfyUI-WanStartEndFramesNative (provides WanImageToVideo)")
            print("   - ComfyUI-GGUF (provides UnetLoaderGGUF)")
            print("\nInstallation commands:")
            print("   comfy-node-install https://github.com/Flow-two/ComfyUI-WanStartEndFramesNative.git")
            print("   comfy-node-install https://github.com/city96/ComfyUI-GGUF.git")
            return False
        else:
            print(f"\n✅ All {len(required_nodes)} required nodes are available!")
            print("✅ Wan video workflow should work correctly!")
            return True

    except requests.ConnectionError:
        print("❌ Cannot connect to ComfyUI API at http://127.0.0.1:8188")
        print("   Make sure ComfyUI is running and accessible")
        return False
    except requests.Timeout:
        print("❌ Timeout connecting to ComfyUI API")
        return False
    except Exception as e:
        print(f"❌ Error checking nodes: {e}")
        return False

if __name__ == "__main__":
    success = check_wan_nodes()
    if not success:
        exit(1)
