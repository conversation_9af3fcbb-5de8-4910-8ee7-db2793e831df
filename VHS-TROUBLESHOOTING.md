# VideoHelperSuite (VHS) Troubleshooting Guide

This guide helps diagnose and fix VideoHelperSuite loading issues in RunPod serverless deployments.

## 🚨 Common Symptoms

- `VHS_VideoCombine` node not found in ComfyUI
- Workflow fails with "node not available" errors
- ComfyUI-VideoHelperSuite appears to install but nodes don't load
- Video generation workflows fail at the output stage

## 🔍 Diagnostic Steps

### Step 1: Build with Enhanced Diagnostics

Use the updated `Dockerfile.wan` which includes:
- Enhanced installation verification
- Python dependency checking
- File structure validation
- Runtime diagnostics

```bash
# Build with diagnostics
./build-wan-video.sh
# or on Windows
./build-wan-video.ps1
```

### Step 2: Verify Installation During Build

The build process now includes automatic verification:
- ✅ Repository cloning
- ✅ Python dependencies installation
- ✅ File structure validation
- ✅ VHS_VideoCombine node detection

### Step 3: Runtime Diagnostics

Run diagnostics inside the container:

```bash
# Quick VHS check
docker run --rm vanna88/wan-video python3 /check-wan-nodes.py

# Comprehensive diagnostics
docker run --rm vanna88/wan-video python3 /diagnose-vhs-runtime.py

# Interactive debugging
docker run -it --rm vanna88/wan-video /bin/bash
```

### Step 4: Check ComfyUI API

Verify nodes are available via API:

```bash
# Inside container or RunPod
curl http://127.0.0.1:8188/object_info | grep -i vhs
```

## 🛠️ Common Fixes

### Fix 1: Python Dependencies

VideoHelperSuite requires specific Python packages:

```dockerfile
# In Dockerfile.wan (already included)
pip install opencv-python-headless imageio imageio-ffmpeg pillow numpy
```

### Fix 2: System Dependencies

Ensure ffmpeg and video libraries are installed:

```dockerfile
# In Dockerfile.wan (already included)
apt-get install -y ffmpeg libgl1 libglib2.0-0
```

### Fix 3: ComfyUI Node Scanning

Force ComfyUI to scan custom nodes:

```dockerfile
# In Dockerfile.wan (already included)
ENV COMFYUI_FORCE_SCAN_CUSTOM_NODES=true
ENV COMFYUI_CUSTOM_NODES_PATH=/comfyui/custom_nodes
```

### Fix 4: Handler Validation

The updated handler now checks VHS availability before running workflows:

```python
# In handler.py (already included)
if "VHS_VideoCombine" in workflow_str:
    vhs_status = check_vhs_nodes_availability()
    if not vhs_status["available"]:
        return {"error": "VHS_VideoCombine node not available"}
```

## 🔧 Advanced Troubleshooting

### Check Custom Node Loading Order

ComfyUI loads custom nodes in alphabetical order. Ensure dependencies are met:

```bash
# List custom nodes directory
ls -la /comfyui/custom_nodes/

# Check for __init__.py files
find /comfyui/custom_nodes -name "__init__.py"
```

### Verify Python Import Path

```python
import sys
print(sys.path)

# Try manual import
sys.path.append('/comfyui/custom_nodes/ComfyUI-VideoHelperSuite')
import videohelpersuite
```

### Check ComfyUI Logs

Look for import errors in ComfyUI startup logs:

```bash
# In RunPod or container
tail -f /comfyui/comfyui.log | grep -i error
```

## 🚀 Deployment Best Practices

### 1. Use the Enhanced Image

```yaml
# In RunPod template
image: vanna88/wan-video:latest
```

### 2. Set Environment Variables

```yaml
environment:
  COMFY_LOG_LEVEL: DEBUG  # For troubleshooting
  COMFYUI_FORCE_SCAN_CUSTOM_NODES: true
```

### 3. Test Before Production

```bash
# Test locally first
docker-compose -f docker-compose.wan.yml up

# Test VHS nodes
curl -X POST http://localhost:8000/runsync \
  -H "Content-Type: application/json" \
  -d @wan-video-proper.json
```

### 4. Monitor Startup

Check that all nodes load during startup:

```bash
# In RunPod logs, look for:
# "Loading custom nodes..."
# "VHS_VideoCombine node is available"
```

## 📊 Verification Checklist

Before deploying to production:

- [ ] Build completes without errors
- [ ] VHS verification script passes
- [ ] ComfyUI API shows VHS nodes
- [ ] Test workflow runs successfully
- [ ] Video output is generated correctly

## 🆘 Emergency Fixes

### If VHS Still Won't Load

1. **Manual Installation**:
   ```bash
   cd /comfyui/custom_nodes
   rm -rf ComfyUI-VideoHelperSuite
   git clone https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git
   cd ComfyUI-VideoHelperSuite
   pip install -r requirements.txt
   ```

2. **Alternative VHS Repository**:
   Try a different fork if the main repo has issues

3. **Minimal Video Output**:
   Use `SaveImage` instead of `VHS_VideoCombine` for debugging

### If Build Fails

1. **Check Docker Resources**:
   - Ensure sufficient disk space (20GB+)
   - Increase Docker memory limit
   - Check network connectivity

2. **Use Cached Build**:
   ```bash
   docker build --cache-from vanna88/wan-video:latest -t vanna88/wan-video .
   ```

## 📞 Getting Help

If issues persist:

1. **Check Build Logs**: Look in `build.log` for specific errors
2. **Run Diagnostics**: Use `/diagnose-vhs-runtime.py` for detailed analysis
3. **Test Locally**: Use Docker Compose for local debugging
4. **Check Dependencies**: Verify all system and Python dependencies

## 🎯 Success Indicators

You'll know VHS is working when:

- ✅ Build completes without VHS-related errors
- ✅ `/check-wan-nodes.py` shows all nodes available
- ✅ ComfyUI API lists `VHS_VideoCombine`
- ✅ `wan-video-proper.json` workflow runs successfully
- ✅ MP4 video files are generated

Remember: The enhanced Dockerfile.wan now includes comprehensive verification at every step, making it much easier to identify and fix VHS loading issues.
