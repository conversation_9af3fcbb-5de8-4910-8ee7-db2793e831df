# Define a build argument for the base image version for easy updates
ARG COMFYUI_VERSION=5.1.0
# Use the -base variant of the worker-comfyui image
FROM runpod/worker-comfyui:${COMFYUI_VERSION}-base AS wan-workflow-base

# Set DEBIAN_FRONTEND to noninteractive to prevent interactive prompts during package installations
ARG DEBIAN_FRONTEND=noninteractive

# Switch to root user for installations, if not already the default
USER root

# Install system libraries that might be needed by custom nodes or their Python dependencies
# Video processing dependencies for Wan workflow (ffmpeg for video encoding/decoding, OpenCV libs for image processing)
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender1 \
    ffmpeg \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create necessary model directories
RUN mkdir -p /comfyui/models/unet /comfyui/models/vae /comfyui/models/clip /comfyui/models/clip_vision

# Install custom nodes using the recommended comfy-node-install method
# This follows the documentation guidelines for custom node installation
RUN echo "🔄 Installing Wan video custom nodes..." && \
    comfy-node-install https://github.com/Flow-two/ComfyUI-WanStartEndFramesNative.git && \
    comfy-node-install https://github.com/city96/ComfyUI-GGUF.git && \
    echo "✅ Custom nodes installation completed"

# Install ComfyUI-VideoHelperSuite using the recommended method
RUN echo "🔄 Installing ComfyUI-VideoHelperSuite..." && \
    comfy-node-install https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git && \
    echo "� Verifying video processing dependencies..." && \
    python -c "import cv2; print('✅ OpenCV imported successfully')" || \
    (echo "⚠️  OpenCV not found, installing manually..." && pip install opencv-python-headless) && \
    python -c "import imageio; print('✅ ImageIO imported successfully')" || \
    (echo "⚠️  ImageIO not found, installing manually..." && pip install imageio imageio-ffmpeg) && \
    echo "✅ ComfyUI-VideoHelperSuite installation completed"

# Copy verification scripts first
COPY check-wan-nodes.py verify-wan-build.py verify-custom-nodes.py diagnose-vhs-runtime.py ./
RUN chmod +x check-wan-nodes.py verify-wan-build.py verify-custom-nodes.py diagnose-vhs-runtime.py

# Create a comprehensive node verification script that searches multiple possible locations
COPY <<EOF /verify-vhs-nodes.py
#!/usr/bin/env python3
import sys
import os

def verify_vhs_installation():
    print("🔍 Verifying VideoHelperSuite installation...")

    # Possible installation locations
    possible_paths = [
        "/comfyui/custom_nodes/ComfyUI-VideoHelperSuite",
        "/comfyui/custom_nodes/ComfyUI-VideoHelperSuite.git",
        "/comfyui/custom_nodes/videohelpersuite",
        "/comfyui/custom_nodes"
    ]

    vhs_found = False
    actual_path = None

    # Search for VideoHelperSuite in possible locations
    for base_path in possible_paths:
        if os.path.exists(base_path):
            print(f"📁 Checking {base_path}...")
            if "VideoHelperSuite" in base_path or base_path.endswith("custom_nodes"):
                if base_path.endswith("custom_nodes"):
                    # Search subdirectories
                    for item in os.listdir(base_path):
                        item_path = os.path.join(base_path, item)
                        if os.path.isdir(item_path) and "video" in item.lower():
                            print(f"✅ Found video-related directory: {item_path}")
                            actual_path = item_path
                            vhs_found = True
                            break
                else:
                    actual_path = base_path
                    vhs_found = True
                    print(f"✅ VideoHelperSuite directory found: {actual_path}")
                    break

    if not vhs_found:
        print("❌ VideoHelperSuite directory not found in any expected location")
        print("📁 Available custom nodes:")
        custom_nodes_path = "/comfyui/custom_nodes"
        if os.path.exists(custom_nodes_path):
            for item in os.listdir(custom_nodes_path):
                print(f"   - {item}")
        return False

    # Search for VHS_VideoCombine node in the found directory
    print("🔍 Searching for VHS_VideoCombine node...")
    vhs_combine_found = False

    for root, dirs, files in os.walk(actual_path):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if 'VHS_VideoCombine' in content:
                            rel_path = os.path.relpath(file_path, actual_path)
                            print(f"✅ Found VHS_VideoCombine in {rel_path}")
                            vhs_combine_found = True
                            break
                except:
                    continue
        if vhs_combine_found:
            break

    if not vhs_combine_found:
        print("⚠️  VHS_VideoCombine node not found, but VideoHelperSuite directory exists")
        print("   This might be normal if the node is defined differently")
        # Don't fail the build for this, as the installation might still work
        return True

    return True

if __name__ == "__main__":
    success = verify_vhs_installation()
    sys.exit(0 if success else 1)
EOF

# Debug: List what was actually installed
RUN echo "📁 Listing custom_nodes directory contents:" && \
    ls -la /comfyui/custom_nodes/ && \
    echo "📁 Searching for video-related directories:" && \
    find /comfyui/custom_nodes -type d -iname "*video*" -o -iname "*vhs*" 2>/dev/null || echo "No video directories found"

RUN chmod +x /verify-vhs-nodes.py && python3 /verify-vhs-nodes.py

# Verify custom nodes were installed successfully
RUN python3 verify-custom-nodes.py

# Download and place models into their respective ComfyUI directories
# The --filename flag ensures the model is saved with the exact name the workflow expects

# UNet (GGUF) - Required by UnetLoaderGGUF node (ID: 56)
RUN comfy model download \
    --url https://huggingface.co/city96/Wan2.1-I2V-14B-480P-gguf/resolve/main/wan2.1-i2v-14b-480p-Q3_K_S.gguf \
    --relative-path models/unet \
    --filename wan2.1-i2v-14b-480p-Q3_K_S.gguf

# VAE - Required by VAELoader node (ID: 39) and WanImageToVideo node (ID: 50)
# Try the primary VAE source first, with fallback to alternative source
RUN comfy model download \
    --url https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors \
    --relative-path models/vae \
    --filename wan_2.1_vae.safetensors || \
    comfy model download \
    --url https://huggingface.co/motion-muse/Wan2.1/resolve/main/Wan2.1_VAE.safetensors \
    --relative-path models/vae \
    --filename wan_2.1_vae.safetensors && \
    ls -la /comfyui/models/vae/ && \
    echo "VAE model verification:" && \
    ls -lh /comfyui/models/vae/wan_2.1_vae.safetensors

# CLIP Vision Model - Required by CLIPVisionLoader node (ID: 49)
RUN comfy model download \
    --url https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors \
    --relative-path models/clip_vision \
    --filename clip_vision_h.safetensors && \
    ls -la /comfyui/models/clip_vision/ && \
    echo "CLIP Vision model verification:" && \
    ls -lh /comfyui/models/clip_vision/clip_vision_h.safetensors

# Text Encoder Model (T5) - Required by CLIPLoader node (ID: 38)
RUN comfy model download \
    --url https://huggingface.co/Evados/DiffSynth-Studio-Lora-Wan2.1-ComfyUI/resolve/main/umt5_xxl_fp8_e4m3fn_scaled.safetensors \
    --relative-path models/clip \
    --filename umt5_xxl_fp8_e4m3fn_scaled.safetensors

# Verification scripts already copied above

# Set environment variables for ComfyUI or worker behavior
# COMFY_LOG_LEVEL defaults to DEBUG in the base image; INFO might be preferred for production
ENV COMFY_LOG_LEVEL=INFO
# ENV REFRESH_WORKER=false # Uncomment and set as needed

# Custom environment variables for enhanced node loading
# These help ensure VideoHelperSuite and other custom nodes load properly
ENV COMFYUI_FORCE_SCAN_CUSTOM_NODES=true
ENV COMFYUI_CUSTOM_NODES_PATH=/comfyui/custom_nodes

# Expose the default ComfyUI port
EXPOSE 8188
# Expose the local API simulation port if SERVE_API_LOCALLY=true is used for testing
EXPOSE 8000

# Use the default start script from the base image, which handles ComfyUI launch
CMD ["/bin/bash", "/start.sh"]