# Define a build argument for the base image version for easy updates
ARG COMFYUI_VERSION=5.1.0
# Use the -base variant of the worker-comfyui image
FROM runpod/worker-comfyui:${COMFYUI_VERSION}-base AS wan-workflow-base

# Set DEBIAN_FRONTEND to noninteractive to prevent interactive prompts during package installations
ARG DEBIAN_FRONTEND=noninteractive

# Switch to root user for installations, if not already the default
USER root

# Install system libraries that might be needed by custom nodes or their Python dependencies
# Video processing dependencies for Wan workflow (ffmpeg for video encoding/decoding, OpenCV libs for image processing)
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender1 \
    ffmpeg \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create necessary model directories
RUN mkdir -p /comfyui/models/unet /comfyui/models/vae /comfyui/models/clip /comfyui/models/clip_vision

# Install custom nodes manually for better control and error handling
# Install ComfyUI-WanStartEndFramesNative
RUN cd /comfyui/custom_nodes && \
    echo "🔄 Installing ComfyUI-WanStartEndFramesNative..." && \
    git clone https://github.com/Flow-two/ComfyUI-WanStartEndFramesNative.git && \
    cd ComfyUI-WanStartEndFramesNative && \
    echo "📁 Checking for requirements.txt..." && \
    if [ -f requirements.txt ]; then \
        echo "🔄 Installing Python dependencies..." && \
        pip install -r requirements.txt; \
    else \
        echo "ℹ️  No requirements.txt found, skipping pip install"; \
    fi && \
    echo "✅ ComfyUI-WanStartEndFramesNative installation completed" && \
    ls -la /comfyui/custom_nodes/ComfyUI-WanStartEndFramesNative/

# Install ComfyUI-GGUF
RUN cd /comfyui/custom_nodes && \
    echo "🔄 Installing ComfyUI-GGUF..." && \
    git clone https://github.com/city96/ComfyUI-GGUF.git && \
    cd ComfyUI-GGUF && \
    echo "📁 Checking for requirements.txt..." && \
    if [ -f requirements.txt ]; then \
        echo "🔄 Installing Python dependencies..." && \
        pip install -r requirements.txt; \
    else \
        echo "ℹ️  No requirements.txt found, installing gguf manually..." && \
        pip install gguf; \
    fi && \
    echo "✅ ComfyUI-GGUF installation completed" && \
    ls -la /comfyui/custom_nodes/ComfyUI-GGUF/

# Install ComfyUI-VideoHelperSuite
RUN cd /comfyui/custom_nodes && \
    echo "🔄 Installing ComfyUI-VideoHelperSuite..." && \
    git clone https://github.com/Kosinkadink/ComfyUI-VideoHelperSuite.git && \
    cd ComfyUI-VideoHelperSuite && \
    echo "📁 Checking for requirements.txt..." && \
    if [ -f requirements.txt ]; then \
        echo "🔄 Installing Python dependencies..." && \
        pip install -r requirements.txt; \
    else \
        echo "ℹ️  No requirements.txt found, installing common video dependencies..." && \
        pip install opencv-python-headless imageio imageio-ffmpeg; \
    fi && \
    echo "✅ ComfyUI-VideoHelperSuite installation completed" && \
    ls -la /comfyui/custom_nodes/ComfyUI-VideoHelperSuite/

# Copy verification scripts first
COPY check-wan-nodes.py verify-wan-build.py verify-custom-nodes.py ./
RUN chmod +x check-wan-nodes.py verify-wan-build.py verify-custom-nodes.py

# Verify custom nodes were installed successfully
RUN python3 verify-custom-nodes.py

# Download and place models into their respective ComfyUI directories
# The --filename flag ensures the model is saved with the exact name the workflow expects

# UNet (GGUF) - Required by UnetLoaderGGUF node (ID: 56)
RUN comfy model download \
    --url https://huggingface.co/city96/Wan2.1-I2V-14B-480P-gguf/resolve/main/wan2.1-i2v-14b-480p-Q3_K_S.gguf \
    --relative-path models/unet \
    --filename wan2.1-i2v-14b-480p-Q3_K_S.gguf

# VAE - Required by VAELoader node (ID: 39) and WanImageToVideo node (ID: 50)
# Try the primary VAE source first, with fallback to alternative source
RUN comfy model download \
    --url https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors \
    --relative-path models/vae \
    --filename wan_2.1_vae.safetensors || \
    comfy model download \
    --url https://huggingface.co/motion-muse/Wan2.1/resolve/main/Wan2.1_VAE.safetensors \
    --relative-path models/vae \
    --filename wan_2.1_vae.safetensors && \
    ls -la /comfyui/models/vae/ && \
    echo "VAE model verification:" && \
    ls -lh /comfyui/models/vae/wan_2.1_vae.safetensors

# CLIP Vision Model - Required by CLIPVisionLoader node (ID: 49)
RUN comfy model download \
    --url https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/clip_vision/clip_vision_h.safetensors \
    --relative-path models/clip_vision \
    --filename clip_vision_h.safetensors && \
    ls -la /comfyui/models/clip_vision/ && \
    echo "CLIP Vision model verification:" && \
    ls -lh /comfyui/models/clip_vision/clip_vision_h.safetensors

# Text Encoder Model (T5) - Required by CLIPLoader node (ID: 38)
RUN comfy model download \
    --url https://huggingface.co/Evados/DiffSynth-Studio-Lora-Wan2.1-ComfyUI/resolve/main/umt5_xxl_fp8_e4m3fn_scaled.safetensors \
    --relative-path models/clip \
    --filename umt5_xxl_fp8_e4m3fn_scaled.safetensors

# Verification scripts already copied above

# Set environment variables for ComfyUI or worker behavior
# COMFY_LOG_LEVEL defaults to DEBUG in the base image; INFO might be preferred for production
ENV COMFY_LOG_LEVEL=INFO
# ENV REFRESH_WORKER=false # Uncomment and set as needed

# Expose the default ComfyUI port
EXPOSE 8188
# Expose the local API simulation port if SERVE_API_LOCALLY=true is used for testing
EXPOSE 8000

# Use the default start script from the base image, which handles ComfyUI launch
CMD ["/bin/bash", "/start.sh"]